use anyhow::Result;
use log::{error, info, warn};
use solana_client::rpc_client::RpcClient;
use solana_sdk::hash::Hash;
use std::{
    sync::{Arc, RwLock},
    time::{Duration, Instant},
};

/// 区块哈希缓存管理器 - async/await 版本
pub struct BlockhashCacheManager {
    client: Arc<RpcClient>,
    blockhash: Arc<RwLock<Hash>>,
    last_fetch_time: Arc<RwLock<Instant>>,
    update_interval: Duration,
}

impl BlockhashCacheManager {
    /// 创建新的区块哈希缓存管理器
    pub fn new(client: Arc<RpcClient>, update_interval_ms: u64) -> Self {
        Self {
            client,
            blockhash: Arc::new(RwLock::new(Hash::default())),
            last_fetch_time: Arc::new(RwLock::new(Instant::now())),
            update_interval: Duration::from_millis(update_interval_ms),
        }
    }

    /// 初始化区块哈希缓存
    pub async fn initialize(&self) -> Result<()> {
        // 尝试立即获取一次区块哈希（失败不影响定时器启动）
        match self.update_blockhash().await {
            Ok(_) => info!("初始区块哈希获取成功"),
            Err(e) => warn!("初始区块哈希获取失败，将依赖定时器更新: {}", e),
        }
        
        // 启动定时器（无论初始获取是否成功）
        self.start_periodic_update();
        
        info!("区块哈希缓存初始化完成，更新间隔: {}ms", self.update_interval.as_millis());
        Ok(())
    }

    /// 启动定期更新任务
    fn start_periodic_update(&self) {
        let client = self.client.clone();
        let blockhash = self.blockhash.clone();
        let last_fetch_time = self.last_fetch_time.clone();
        let update_interval = self.update_interval;

        // 启动异步定时任务
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(update_interval);
            
            loop {
                interval.tick().await;
                
                // 尝试更新区块哈希
                let start = Instant::now();
                match client.get_latest_blockhash() {
                    Ok(new_blockhash) => {
                        let duration = start.elapsed();
                        
                        // 更新缓存
                        if let Ok(mut bh) = blockhash.write() {
                            *bh = new_blockhash;
                        }
                        if let Ok(mut lft) = last_fetch_time.write() {
                            *lft = Instant::now();
                        }
                        
                        info!("更新区块哈希: {}, 耗时: {}ms", new_blockhash, duration.as_millis());
                    }
                    Err(e) => {
                        error!("更新区块哈希失败: {}", e);
                        // 失败了也没关系，下次继续尝试
                    }
                }
            }
        });
    }

    /// 手动更新区块哈希
    pub async fn update_blockhash(&self) -> Result<()> {
        let start = Instant::now();
        let blockhash = self.client.get_latest_blockhash()?;
        let duration = start.elapsed();

        {
            let mut bh = self.blockhash.write().unwrap();
            *bh = blockhash;
        }
        {
            let mut lft = self.last_fetch_time.write().unwrap();
            *lft = Instant::now();
        }

        info!("更新区块哈希: {}, 耗时: {}ms", blockhash, duration.as_millis());
        Ok(())
    }

    /// 获取缓存的区块哈希
    pub fn get_blockhash(&self) -> Hash {
        let blockhash = self.blockhash.read().unwrap();
        *blockhash
    }

    /// 获取上次更新时间
    pub fn get_last_fetch_time(&self) -> Instant {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        *last_fetch_time
    }

    /// 获取距离上次更新的时间间隔(毫秒)
    pub fn get_time_since_last_update(&self) -> u128 {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        last_fetch_time.elapsed().as_millis()
    }

    /// 强制立即更新区块哈希
    pub async fn force_update(&self) -> Result<Hash> {
        self.update_blockhash().await?;
        Ok(self.get_blockhash())
    }
}
