use crate::consts;
use crate::types::*;
use anyhow::{Context, Result};
use log::{error, info};
use std::{
    sync::{Arc, Mutex, RwLock},
    thread,
    time::{Duration, Instant},
};

/// SOL/USDC价格缓存管理器
pub struct PriceCacheManager {
    http_client: reqwest::Client,
    sol_usdc_price: Arc<RwLock<f64>>,
    last_fetch_time: Arc<RwLock<Instant>>,
    update_interval: Duration,
    is_updating: Arc<Mutex<bool>>,
    is_running: Arc<Mutex<bool>>,
}

impl PriceCacheManager {
    /// 创建新的价格缓存管理器
    pub fn new(update_interval_ms: u64) -> Self {
        Self {
            http_client: reqwest::Client::new(),
            sol_usdc_price: Arc::new(RwLock::new(consts::SOL_TO_USDC_PRICE)), // 使用常量中的默认价格作为初始值
            last_fetch_time: Arc::new(RwLock::new(Instant::now())),
            update_interval: Duration::from_millis(update_interval_ms),
            is_updating: Arc::new(Mutex::new(false)),
            is_running: Arc::new(Mutex::new(false)),
        }
    }

    /// 初始化价格缓存
    pub async fn initialize(&self) -> Result<()> {
        // 立即获取一次价格
        self.update_price().await?;
        
        // 设置定时器，按指定间隔更新价格
        self.start_periodic_update();
        
        info!("SOL/USDC价格缓存初始化完成，更新间隔: {}ms", self.update_interval.as_millis());
        Ok(())
    }

    /// 开始定期更新
    pub fn start_periodic_update(&self) {
        let mut is_running = self.is_running.lock().unwrap();
        if *is_running {
            return;
        }
        *is_running = true;
        
        // 克隆需要在线程中使用的数据
        let update_interval = self.update_interval;
        let sol_usdc_price = self.sol_usdc_price.clone();
        let last_fetch_time = self.last_fetch_time.clone();
        let is_updating = self.is_updating.clone();
        let http_client = self.http_client.clone();
        
        // 创建更新线程
        thread::spawn(move || {
            loop {
                thread::sleep(update_interval);
                
                // 防止重复更新
                let mut updating = is_updating.lock().unwrap();
                if *updating {
                    continue;
                }
                *updating = true;
                
                // 异步更新价格 - 使用多线程 runtime
                let rt = tokio::runtime::Builder::new_multi_thread()
                    .enable_all()
                    .build()
                    .unwrap();
                    
                rt.block_on(async {
                    let start = Instant::now();
                    // 第一次尝试获取价格
                    match fetch_sol_usdc_price(&http_client).await {
                        Ok(price) => {
                            let duration = start.elapsed();
                            
                            // 更新缓存
                            if let Ok(mut cached_price) = sol_usdc_price.write() {
                                *cached_price = price;
                            }
                            if let Ok(mut lft) = last_fetch_time.write() {
                                *lft = Instant::now();
                            }
                            
                            info!("更新SOL/USDC价格: {}, 耗时: {}ms", price, duration.as_millis());
                        }
                        Err(e) => {
                            error!("更新SOL/USDC价格失败: {}", e);
                            // 失败时不更新缓存，继续使用上次的缓存价格
                        }
                    }
                });
                
                *updating = false;
            }
        });
    }

    /// 停止定期更新
    pub fn stop_periodic_update(&self) {
        let mut is_running = self.is_running.lock().unwrap();
        *is_running = false;
    }

    /// 更新价格 (带重试机制)
    pub async fn update_price(&self) -> Result<()> {
        let mut is_updating = self.is_updating.lock().unwrap();
        *is_updating = true;
        
        let start = Instant::now();
        
        // 第一次尝试获取价格
        let price_result = fetch_sol_usdc_price(&self.http_client).await;
        
        // 如果失败，重试一次
        let price = match price_result {
            Ok(p) => p,
            Err(e) => {
                error!("更新SOL/USDC价格失败，准备重试: {}", e);
                // 等待短暂时间后重试
                tokio::time::sleep(Duration::from_millis(500)).await;
                match fetch_sol_usdc_price(&self.http_client).await {
                    Ok(p) => p,
                    Err(e) => {
                        error!("重试更新SOL/USDC价格再次失败: {}", e);
                        *is_updating = false;
                        return Err(e.into());
                    }
                }
            }
        };
        
        let duration = start.elapsed();
        
        {
            let mut cached_price = self.sol_usdc_price.write().unwrap();
            *cached_price = price;
        }
        {
            let mut lft = self.last_fetch_time.write().unwrap();
            *lft = Instant::now();
        }
        
        info!("更新SOL/USDC价格: {}, 耗时: {}ms", price, duration.as_millis());
        
        *is_updating = false;
        Ok(())
    }

    /// 获取缓存的SOL/USDC价格
    pub fn get_sol_usdc_price(&self) -> f64 {
        let price = self.sol_usdc_price.read().unwrap();
        *price
    }

    /// 获取上次更新时间
    pub fn get_last_fetch_time(&self) -> Instant {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        *last_fetch_time
    }

    /// 获取距离上次更新的时间间隔(毫秒)
    pub fn get_time_since_last_update(&self) -> u128 {
        let last_fetch_time = self.last_fetch_time.read().unwrap();
        last_fetch_time.elapsed().as_millis()
    }

    /// 强制立即更新价格
    pub async fn force_update(&self) -> Result<f64> {
        self.update_price().await?;
        Ok(self.get_sol_usdc_price())
    }
    
    /// 转换USDC利润为SOL lamports (简化版本)
    pub fn convert_usdc_profit_to_sol_lamports(&self, usdc_profit: u64) -> u64 {
        let sol_usdc_price = self.get_sol_usdc_price();
        let usdc_profit_f64 = usdc_profit as f64;
        // 直接使用1000作为乘数，因为9(SOL精度) - 6(USDC精度) = 3，所以是10^3 = 1000
        let sol_lamports = usdc_profit_f64 / sol_usdc_price * 1000.0;
        
        sol_lamports.floor() as u64
    }
}

impl Drop for PriceCacheManager {
    fn drop(&mut self) {
        self.stop_periodic_update();
    }
}

/// 从Jupiter API获取最新的SOL/USDC价格
async fn fetch_sol_usdc_price(client: &reqwest::Client) -> Result<f64> {
    let quote_params = QuoteParams {
        input_mint: "So11111111111111111111111111111111111111112".to_string(), // WSOL
        output_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v".to_string(), // USDC
        amount: "**********".to_string(),  // 1 SOL (9位精度)
        only_direct_routes: true,
        slippage_bps: 0,
        max_accounts: 20,
    };

    let response = client
        .get(format!("{}/quote", consts::JUP_V6_API_BASE_URL.as_str()))
        .query(&quote_params)
        .send()
        .await
        .context("请求Jupiter API失败")?;
    
    let quote_resp: QuoteResponse = response.json().await
        .context("解析Jupiter API响应失败")?;
    
    // 将输出金额转换为数值
    let usdc_amount = quote_resp.out_amount.parse::<f64>()
        .context("解析USDC金额失败")?;
    
    // USDC有6位精度，计算原始SOL/USDC汇率
    let price = usdc_amount / 1_000_000.0;
    
    Ok(price)
} 