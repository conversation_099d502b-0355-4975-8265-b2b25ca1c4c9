use crate::config::{ArbPairConfig, ConfigManager};
use crate::consts;
use crate::types::*;
use crate::jito_tip_manager::JitoTipManager;
use crate::alt_cache_manager::AltCacheManager;
use crate::blockhash_cache_manager::BlockhashCacheManager;
use crate::price_cache_manager::PriceCacheManager;
use crate::IpRotator::IpRotator;
use crate::ProxyRotator::ProxyRotator;
use anyhow::Result;
use base64::Engine;
use log;
use rand::seq::SliceRandom;
use rand::thread_rng;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    compute_budget::ComputeBudgetInstruction,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{read_keypair_file, Keypair},
    signer::Signer,
    system_instruction,
    transaction::VersionedTransaction,
};
use std::{env, str::FromStr, time::Instant, fs, path::Path, sync::Arc};

pub struct ArbitrageBot {
    client: RpcClient,
    http_client: reqwest::Client,
    payer: Keypair,
    jito_tip_manager: JitoTipManager,
    alt_cache_manager: AltCacheManager,
    blockhash_cache_manager: BlockhashCacheManager,
    price_cache_manager: PriceCacheManager,
    ip_rotator: IpRotator,
    proxy_rotator: Option<ProxyRotator>,
    config_manager: ConfigManager,
}

impl ArbitrageBot {
    /// 创建新的多代币对套利机器人
    pub fn new(config_path: &Path) -> Result<Self> {
        // 加载配置文件
        let config_manager = ConfigManager::new(config_path)?;
        
        // 获取密钥路径
        let keypair_path = env::var("KEYPAIR_PATH").expect("必须设置KEYPAIR_PATH环境变量");
        let payer = read_keypair_file(&keypair_path).expect("无法读取密钥文件");

        log::info!("付款账户地址: {:?}", bs58::encode(payer.pubkey()).into_string());
        
        // 确保数据目录存在
        let data_dir = Path::new(consts::DATA_DIR);
        if !data_dir.exists() {
            fs::create_dir_all(data_dir)?;
        }
        
        // 获取RPC URL
        let rpc_url = config_manager.get_rpc_url();
        
        // 创建RPC客户端
        let client = RpcClient::new_with_commitment(
            rpc_url.clone(),
            CommitmentConfig::processed(),
        );
        
        // 创建RPC客户端的Arc包装，用于缓存管理器
        let client_arc = Arc::new(RpcClient::new_with_commitment(
            rpc_url,
            CommitmentConfig::processed(),
        ));
        
        // 初始化 JitoTipManager
        let jito_tip_manager = JitoTipManager::new(consts::TIP_ACCOUNTS_COUNT, data_dir)?;
        
        // 初始化 AltCacheManager
        let alt_cache_manager = AltCacheManager::new(client_arc.clone(), data_dir)?;
        
        // 初始化 BlockhashCacheManager (1秒更新一次)
        let blockhash_cache_manager = BlockhashCacheManager::new(client_arc, 1000);

        // 初始化 PriceCacheManager
        let price_update_interval = config_manager.get_price_update_interval(); 
        let price_cache_manager = PriceCacheManager::new(price_update_interval);

        // 初始化 IP轮询器
        let ip_rotator = IpRotator::new();

        // 根据配置决定是否创建代理轮询器
        let proxy_rotator = if config_manager.is_proxy_enabled() {
            Some(ProxyRotator::new())
        } else {
            None
        };

        Ok(Self {
            client,
            http_client: reqwest::Client::new(),
            payer,
            jito_tip_manager,
            alt_cache_manager,
            blockhash_cache_manager,
            price_cache_manager,
            ip_rotator,
            proxy_rotator,
            config_manager,
        })
    }

    /// USDC利润转换为SOL lamports - 使用价格缓存管理器
    pub fn convert_usdc_profit_to_sol_lamports(&self, usdc_profit: u64) -> u64 {
        // 使用价格缓存管理器的转换方法
        self.price_cache_manager.convert_usdc_profit_to_sol_lamports(usdc_profit)
    }

    /// 检查单个套利机会
    pub async fn check_arbitrage_opportunity(&self, pair_config: &ArbPairConfig) -> Result<()> {
        let start = Instant::now();
        let pair_name = pair_config.name.clone().unwrap_or_else(|| 
            format!("{}->{}", pair_config.input_mint, pair_config.output_mint));

        // 从金额列表中随机选择一个金额 - 将随机生成器限制在局部作用域内
        let amount = {
            let mut rng = thread_rng();
            match pair_config.amounts.choose(&mut rng) {
                Some(&amount) => amount,
                None => {
                    return Err(anyhow::anyhow!("套利对 {} 的amounts列表为空", pair_name));
                }
            }
        }; // rng 在此作用域结束时被释放，不会跨越 await

        // 检查是否启用闪电贷
        let enable_kamino = self.config_manager.is_kamino_enabled_for_pair(pair_config);

        log::debug!("套利对: {}, 使用金额: {}, 闪电贷: {}", pair_name, amount, enable_kamino);
        
        // Quote 0: INPUT -> OUTPUT
        let quote0_params = QuoteParams {
            input_mint: pair_config.input_mint.clone(),
            output_mint: pair_config.output_mint.clone(),
            amount: amount.to_string(),
            only_direct_routes: pair_config.only_direct_routes,
            slippage_bps: 0,
            max_accounts: pair_config.max_accounts,
        };
        
        // 获取第一个报价
        let quote0_resp = self.get_quote(&quote0_params).await?;

        // Quote 1: OUTPUT -> INPUT
        let quote1_params = QuoteParams {
            input_mint: pair_config.output_mint.clone(),
            output_mint: quote0_resp.input_mint.clone(),
            amount: quote0_resp.out_amount.clone(),
            only_direct_routes: true,
            slippage_bps: 0,
            max_accounts: 20,
        };
        
        // 获取第二个报价
        let quote1_resp = self.get_quote(&quote1_params).await?;

        // 计算潜在利润
        let quote1_out_amount = quote1_resp.out_amount.parse::<u64>()?;
        let quote0_in_amount = amount;
        
        if quote1_out_amount <= quote0_in_amount {
            log::debug!(
                "套利对 {} 不盈利，跳过。差额: -{}",
                pair_name,
                quote0_in_amount - quote1_out_amount
            );
            return Ok(());
        }
        
        // 计算利润
        let profit = quote1_out_amount - quote0_in_amount;
        
        // 根据硬编码公钥判断代币类型
        let is_wsol = quote0_resp.input_mint == "So11111111111111111111111111111111111111112";
        
        // 统一计算difflamports
        let difflamports = if is_wsol {
            // WSOL路径：直接使用profit作为lamports
            profit
        } else {
            // USDC路径：转换为SOL lamports
            self.convert_usdc_profit_to_sol_lamports(profit)
        };
        
        // 打印日志 - 根据代币类型显示不同信息
        if is_wsol {
            log::info!("{}: WSOL profit: {} lamports", pair_name, profit);
        } else {
            // 获取当前SOL价格信息
            log::info!("{}: USDC profit: {}, 转换为: {} lamports", pair_name, profit, difflamports);
        }
        
        // 获取Jito小费百分比列表
        let jito_tip_percentages = &self.config_manager.get_config().jito_tip_percentages;
        
        // 动态计算Jito小费 - 在局部作用域内创建和使用随机数生成器
        let jito_tip = {
            let mut rng = thread_rng();
            let percentage = *jito_tip_percentages.choose(&mut rng).unwrap_or(&0.5);
            let jito_tip = (difflamports as f64 * percentage) as u64;
            jito_tip.min(consts::MAX_TIP_LAMPORTS)
        };
        
        // 计算闪电贷手续费（如果启用）
        let flashloan_fee_lamports = if enable_kamino {
            let lending_amount = amount; // 借款金额等于交易金额
            crate::kamino::calculate_flashloan_fee_lamports(lending_amount, is_wsol, &self.price_cache_manager)
        } else {
            0
        };

        log::info!("{}: Jito小费 (lamports): {}", pair_name, jito_tip);

        if enable_kamino {
            log::info!("{}: 闪电贷手续费 (lamports): {}", pair_name, flashloan_fee_lamports);
        }

        // 统一使用lamports阈值
        const THRESHOLD_LAMPORTS: u64 = 500;  // 例如 0.000005 SOL

        // 计算真实利润 = difflamports - jito_tip - BASE_TX_FEE - flashloan_fee
        const BASE_TX_FEE: u64 = 10000;  // Solana 基础交易费用 (lamports)
        let real_profit = difflamports
            .saturating_sub(jito_tip)
            .saturating_sub(BASE_TX_FEE)
            .saturating_sub(flashloan_fee_lamports);
        
        // 打印真实利润信息
        log::info!("{}: 真实利润 (lamports): {}", pair_name, real_profit);
        
        // 使用真实利润与阈值比较
        if real_profit > THRESHOLD_LAMPORTS {
            // 执行套利交易
            log::info!("{}: 执行套利交易...", pair_name);
            // 传递lending_amount参数
            let lending_amount = if enable_kamino { amount } else { 0 };

            self.execute_arbitrage(quote0_resp, quote1_resp, jito_tip, lending_amount, enable_kamino).await?;
            
            let duration = start.elapsed();
            log::info!("{}: 套利完成，总耗时: {}ms", pair_name, duration.as_millis());
        }

        Ok(())
    }
    
    /// 执行套利交易（支持闪电贷）
    async fn execute_arbitrage(
        &self,
        quote0: QuoteResponse,
        quote1: QuoteResponse,
        jito_tip: u64,
        lending_amount: u64,  // 新增：闪电贷金额
        enable_kamino: bool,  // 新增：是否启用闪电贷
    ) -> Result<()> {
        // 获取一个小号代付账户
        let tip_account = self.jito_tip_manager.get_next_tip_account();
        log::info!("使用小费账户: {}", tip_account.pubkey().to_string());
        
        // 合并两个报价，创建一个完整的交易路径
        let mut merged_quote = quote0.clone();
        merged_quote.output_mint = quote1.output_mint;
        merged_quote.out_amount = quote1.out_amount;
        merged_quote.other_amount_threshold =
            (quote0.other_amount_threshold.parse::<u64>()? + jito_tip).to_string();        
        merged_quote.price_impact_pct = 0.0.to_string();
        merged_quote.route_plan = [quote0.route_plan, quote1.route_plan].concat();

        // 准备 Jupiter API 的交换数据
        let swap_data = SwapData {
            user_public_key: bs58::encode(self.payer.pubkey()).into_string(),
            wrap_and_unwrap_sol: false,
            use_shared_accounts: false,
            compute_unit_price_micro_lamports: 1,
            dynamic_compute_unit_limit: true,
            skip_user_accounts_rpc_calls: true,
            quote_response: merged_quote,
        };

        // 从 Jupiter 获取交换指令
        let instructions_resp: SwapInstructionResponse =
            self.get_swap_instructions(&swap_data).await?;

        // 构建主账户交易指令列表
        let mut main_tx_instructions = Vec::new();

        // 1. 计算预算指令（考虑闪电贷额外计算单元）
        let base_compute_limit = instructions_resp.compute_unit_limit;
        let compute_limit = if enable_kamino {
            let kamino_config = self.config_manager.get_kamino_config().unwrap();
            base_compute_limit + kamino_config.additional_compute_units
        } else {
            base_compute_limit
        };

        let compute_budget_ix = ComputeBudgetInstruction::set_compute_unit_limit(compute_limit);
        main_tx_instructions.push(compute_budget_ix);

        // 2. 如果启用闪电贷，添加借款指令
        if enable_kamino {
            let kamino_config = self.config_manager.get_kamino_config().unwrap();
            let is_wsol = quote0.input_mint == "So11111111111111111111111111111111111111112";

            if is_wsol {
                // SOL闪电贷
                let borrow_ix = crate::kamino::get_sol_kamino_flashloan_borrow_ix(
                    &self.payer.pubkey(),
                    Pubkey::from_str(&kamino_config.sol_destination_token_account)?,
                    lending_amount,
                )?;
                main_tx_instructions.push(borrow_ix);
            } else {
                // USDC闪电贷
                let borrow_ix = crate::kamino::get_usdc_kamino_flashloan_borrow_ix(
                    &self.payer.pubkey(),
                    Pubkey::from_str(&kamino_config.usdc_destination_token_account)?,
                    lending_amount,
                )?;
                main_tx_instructions.push(borrow_ix);
            }
        }

        // 3. 添加设置指令
        //for setup_ix in instructions_resp.setup_instructions {
        //    main_tx_instructions.push(self.convert_instruction_data(setup_ix)?);
        //}

        // 4. 添加交换指令
        main_tx_instructions.push(self.convert_instruction_data(instructions_resp.swap_instruction)?);

        // 5. 如果启用闪电贷，添加还款指令
        if enable_kamino {
            let kamino_config = self.config_manager.get_kamino_config().unwrap();
            let is_wsol = quote0.input_mint == "So11111111111111111111111111111111111111112";

            // 计算借款指令的索引
            let borrow_instruction_index = 1u8; // compute_budget(0) + flashloan_borrow(1)

            if is_wsol {
                // SOL闪电贷还款
                let repay_ix = crate::kamino::get_sol_kamino_flashloan_repay_ix(
                    &self.payer.pubkey(),
                    Pubkey::from_str(&kamino_config.sol_source_token_account)?,
                    lending_amount, // 还款金额等于借款金额
                    borrow_instruction_index,
                )?;
                main_tx_instructions.push(repay_ix);
            } else {
                // USDC闪电贷还款
                let repay_ix = crate::kamino::get_usdc_kamino_flashloan_repay_ix(
                    &self.payer.pubkey(),
                    Pubkey::from_str(&kamino_config.usdc_source_token_account)?,
                    lending_amount, // 还款金额等于借款金额
                    borrow_instruction_index,
                )?;
                main_tx_instructions.push(repay_ix);
            }
        }

        // 6. 从主账户向小号转账的指令（包含 tip + 返回金额 + 网络费用）
        let transfer_to_tip_account_ix = system_instruction::transfer(
            &self.payer.pubkey(),
            &tip_account.pubkey(),
            jito_tip + consts::RETURN_AMOUNT + consts::NETWORK_FEE,
        );
        main_tx_instructions.push(transfer_to_tip_account_ix);

        // 获取缓存的区块哈希
        log::info!("获取缓存的区块哈希...");
        let start_blockhash_time = Instant::now();
        let blockhash = self.blockhash_cache_manager.get_blockhash();
        let blockhash_age = self.blockhash_cache_manager.get_time_since_last_update();
        let end_blockhash_time = start_blockhash_time.elapsed();
        
        log::info!(
            "获取区块哈希耗时: {}ms, 区块哈希: {}, 距上次更新: {}ms",
            end_blockhash_time.as_millis(),
            blockhash,
            blockhash_age
        );

        // 转换地址查找表
        let address_lookup_tables = self
            .get_address_lookup_tables(&instructions_resp.address_lookup_table_addresses)
            .await?;

        // 创建主账户交易消息
        let main_message = solana_sdk::message::v0::Message::try_compile(
            &self.payer.pubkey(),
            &main_tx_instructions,
            &address_lookup_tables,
            blockhash,
        )?;

        // 创建并签名主账户交易
        let main_transaction = VersionedTransaction::try_new(
            solana_sdk::message::VersionedMessage::V0(main_message),
            &[&self.payer],
        )?;

        // 构建小号代付交易指令列表
        let mut tip_tx_instructions = Vec::new();

        // 1. 向 Jito 支付小费
        let random_jito_tip_account = consts::get_random_jito_tip_account();
        log::info!("使用Jito小费地址: {}", random_jito_tip_account);
        let pay_jito_tip_ix = system_instruction::transfer(
            &tip_account.pubkey(),
            &Pubkey::from_str(random_jito_tip_account)?,
            jito_tip,
        );
        tip_tx_instructions.push(pay_jito_tip_ix);

        // 2. 将固定金额返回给主账户
        let return_to_main_ix = system_instruction::transfer(
            &tip_account.pubkey(),
            &self.payer.pubkey(),
            consts::RETURN_AMOUNT,
        );
        tip_tx_instructions.push(return_to_main_ix);

        // 创建小号代付交易消息（不使用地址查找表）
        let tip_message = solana_sdk::message::v0::Message::try_compile(
            &tip_account.pubkey(),
            &tip_tx_instructions,
            &[], // 小号交易不需要地址查找表
            blockhash,
        )?;

        // 创建并签名小号代付交易
        let tip_transaction = VersionedTransaction::try_new(
            solana_sdk::message::VersionedMessage::V0(tip_message),
            &[&tip_account],
        )?;

        log::info!("主交易: {:?}", main_transaction.signatures[0]);
        log::info!("小号交易: {:?}", tip_transaction.signatures[0]);

        // 将两个交易作为 bundle 发送到 Jito
        self.send_bundle_to_jito(vec![main_transaction, tip_transaction]).await?;

        Ok(())
    }

    async fn get_quote(&self, params: &QuoteParams) -> Result<QuoteResponse> {
        let jupiter_api_url = self.config_manager.get_jupiter_api_url();
        let response: QuoteResponse = self
            .http_client
            .get(format!("{}/quote", jupiter_api_url))
            .query(&params)
            .send()
            .await?
            .json()
            .await?;
        Ok(response)
    }

    async fn get_swap_instructions(&self, params: &SwapData) -> Result<SwapInstructionResponse> {
        let jupiter_api_url = self.config_manager.get_jupiter_api_url();
        let response: SwapInstructionResponse = self
            .http_client
            .post(format!("{}/swap-instructions", jupiter_api_url))
            .json(&params)
            .send()
            .await?
            .json()
            .await?;
        Ok(response)
    }

    async fn send_bundle_to_jito(&self, transactions: Vec<VersionedTransaction>) -> Result<()> {
        // Serialize transactions for Jito bundle
        let serialized_txs: Vec<Vec<u8>> = transactions
            .iter()
            .map(|tx| bincode::serialize(tx).map_err(anyhow::Error::from))
            .collect::<Result<_>>()?;
        let base58_txs = serialized_txs
            .iter()
            .map(|tx| bs58::encode(tx).into_string())
            .collect::<Vec<_>>();

        // Prepare bundle request
        let bundle_request = serde_json::json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": "sendBundle",
            "params": [base58_txs]
        });

        // 根据配置选择发送方式
        if self.config_manager.is_proxy_enabled() {
            self.send_via_proxy(bundle_request).await?;
        } else {
            self.send_via_ip_rotation(bundle_request).await?;
        }

        Ok(())
    }

    fn convert_instruction_data(&self, ix_data: InstructionData) -> Result<Instruction> {
        let program_id = Pubkey::from_str(&ix_data.program_id)?;

        let accounts: Vec<AccountMeta> = ix_data
            .accounts
            .into_iter()
            .map(|acc| {
                let pubkey = Pubkey::from_str(&acc.pubkey).expect("Failed to parse pubkey");
                AccountMeta {
                    pubkey,
                    is_signer: acc.is_signer,
                    is_writable: acc.is_writable,
                }
            })
            .collect();

        // 使用 base64 0.21.0 版本的新 API
        let data = base64::engine::general_purpose::STANDARD.decode(&ix_data.data)?;

        Ok(Instruction {
            program_id,
            accounts,
            data,
        })
    }

    async fn get_address_lookup_tables(
        &self,
        addresses: &[String],
    ) -> Result<Vec<solana_sdk::address_lookup_table_account::AddressLookupTableAccount>> {
        // 使用 ALT 缓存管理器获取地址查找表
        self.alt_cache_manager.get_address_lookup_table_accounts(addresses).await
    }

    // 添加预热 ALT 缓存的方法
    pub async fn preload_alt_cache(&self) -> Result<()> {
        let alt_list_file = Path::new("src/LookupTableAccount.rs");
        self.alt_cache_manager.initialize_from_address_list(alt_list_file).await?;
        log::info!("ALT 缓存预热完成");
        Ok(())
    }
    
    // 初始化区块哈希缓存
    pub async fn initialize_blockhash_cache(&self) -> Result<()> {
        self.blockhash_cache_manager.initialize().await?;
        log::info!("区块哈希缓存初始化完成");
        Ok(())
    }

    // 初始化价格缓存
    pub async fn initialize_price_cache(&self) -> Result<()> {
        self.price_cache_manager.initialize().await?;
        log::info!("价格缓存初始化完成");
        Ok(())
    }

    // 初始化IP轮询器
    pub async fn initialize_ip_rotator(&self) -> Result<()> {
        // 先初始化IP轮询器
        self.ip_rotator.initialize().await?;
        
        // 获取配置的Jito端点并设置
        let jito_endpoints = self.config_manager.get_jito_rpc_urls();
        self.ip_rotator.set_jito_endpoints(jito_endpoints);
        
        log::info!("IP轮询器初始化完成");
        Ok(())
    }
    
    /// 获取配置管理器
    pub fn get_config_manager(&self) -> &ConfigManager {
        &self.config_manager
    }

    /// 初始化发送器（根据配置选择IP轮询或代理）
    pub async fn initialize_sender(&self) -> Result<()> {
        if self.config_manager.is_proxy_enabled() {
            log::info!("配置启用代理，初始化代理轮询器...");
            self.initialize_proxy_rotator().await?;
            log::info!("代理轮询器初始化完成，IP轮询器将被禁用");
        } else {
            log::info!("配置未启用代理，初始化IP轮询器...");
            self.initialize_ip_rotator().await?;
            log::info!("IP轮询器初始化完成");
        }
        Ok(())
    }

    /// 初始化代理轮询器
    async fn initialize_proxy_rotator(&self) -> Result<()> {
        if let Some(proxy_rotator) = &self.proxy_rotator {
            // 设置Jito端点（直接使用配置中的列表）
            let jito_endpoints = self.config_manager.get_active_jito_endpoints();
            proxy_rotator.set_jito_endpoints(jito_endpoints);

            // 加载代理列表
            let proxy_file = self.config_manager.get_proxy_file();
            proxy_rotator.load_proxies_from_file(&proxy_file).await?;

            // 测试代理连通性
            proxy_rotator.test_proxy_connectivity_to_jito().await?;

            // 预加载代理客户端
            proxy_rotator.preload_proxy_clients().await?;

            log::info!("代理轮询器初始化完成");
        }
        Ok(())
    }

    /// 通过代理发送（并发版本）
    async fn send_via_proxy(&self, bundle_request: serde_json::Value) -> Result<()> {
        if let Some(proxy_rotator) = &self.proxy_rotator {
            // 使用新的并发发送方法
            let results = proxy_rotator.send_jito_bundles_via_proxy(bundle_request).await?;

            // 处理发送结果（与IP轮询器保持一致）
            let mut success_count = 0;
            for (endpoint, response) in &results {
                if let Some(bundle_id) = response.get("result").and_then(|r| r.as_str()) {
                    log::info!("通过代理发送到端点 {} 成功，bundle id: {}", endpoint, bundle_id);
                    success_count += 1;
                } else if let Some(error) = response.get("error") {
                    log::warn!("通过代理发送到端点 {} 失败，错误: {}", endpoint, error);
                } else {
                    log::warn!("通过代理发送到端点 {} 失败，未知错误", endpoint);
                }
            }

            if success_count > 0 {
                log::info!("通过代理成功发送到 {}/{} 个端点", success_count, results.len());
                Ok(())
            } else {
                Err(anyhow::anyhow!("通过代理向所有端点发送均失败"))
            }
        } else {
            Err(anyhow::anyhow!("代理轮询器未初始化"))
        }
    }

    /// 通过IP轮询发送（原有方式）
    async fn send_via_ip_rotation(&self, bundle_request: serde_json::Value) -> Result<()> {
        let results = self.ip_rotator.send_jito_bundles(bundle_request).await?;

        let mut success_count = 0;
        for (endpoint, response) in &results {
            if let Some(bundle_id) = response.get("result").and_then(|r| r.as_str()) {
                log::info!("通过IP轮询发送到端点 {} 成功，bundle id: {}", endpoint, bundle_id);
                success_count += 1;
            } else if let Some(error) = response.get("error") {
                log::warn!("通过IP轮询发送到端点 {} 失败，错误: {}", endpoint, error);
            } else {
                log::warn!("通过IP轮询发送到端点 {} 失败，未知错误", endpoint);
            }
        }

        if success_count > 0 {
            log::info!("通过IP轮询成功发送到 {}/{} 个端点", success_count, results.len());
            Ok(())
        } else {
            Err(anyhow::anyhow!("通过IP轮询向所有端点发送均失败"))
        }
    }
}