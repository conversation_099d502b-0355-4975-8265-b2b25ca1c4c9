use anyhow::Result;
use local_ip_address::{list_afinet_netifas, local_ip};
use log::{info, warn};
use reqwest::{Client, ClientBuilder};
use serde_json::Value;
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::process::Command;
use futures::future;

/// IP轮询器 - 管理多个本地IP地址并轮询使用
pub struct IpRotator {
    available_ips: Arc<Mutex<Vec<IpAddr>>>,
    current_index: Arc<Mutex<usize>>,
    // 为每个IP缓存一个HTTP客户端
    client_cache: Arc<Mutex<HashMap<IpAddr, Client>>>,
    default_client: Client,
    // Jito端点列表
    jito_endpoints: Arc<Mutex<Vec<String>>>,
}

impl IpRotator {
    /// 创建新的IP轮询器实例
    pub fn new() -> Self {
        let default_client = ClientBuilder::new()
            .timeout(Duration::from_secs(5))
            .pool_idle_timeout(Duration::from_secs(90))  // 连接在池中保持90秒
            .tcp_keepalive(Duration::from_secs(60))      // TCP保活60秒
            .pool_max_idle_per_host(10)                  // 每个主机最多保持10个空闲连接
            .build()
            .expect("Failed to build HTTP client");

        Self {
            available_ips: Arc::new(Mutex::new(Vec::new())),
            current_index: Arc::new(Mutex::new(0)),
            client_cache: Arc::new(Mutex::new(HashMap::new())),
            default_client,
            jito_endpoints: Arc::new(Mutex::new(Vec::new())),
        }
    }

    /// 初始化IP轮询器
    pub async fn initialize(&self) -> Result<()> {
        info!("开始扫描系统IPv4地址...");
        self.scan_system_ips().await?;

        // 如果没有找到可用IP，使用默认IP
        {
            let ips = self.available_ips.lock().unwrap();
            if ips.is_empty() {
                drop(ips); // 释放锁
                let default_ip = local_ip()?;
                info!("未找到任何可用IP地址，将使用默认IP: {}", default_ip);
                let mut ips = self.available_ips.lock().unwrap();
                ips.push(default_ip);
            } else {
                info!("IP扫描完成，共发现 {} 个IP地址:", ips.len());
                for (i, ip) in ips.iter().enumerate() {
                    info!("[{}] {}", i + 1, ip);
                }
            }
        }

        // 使用并发方式测试IP连通性
        self.test_ip_connectivity_to_jito().await?;
        
        // 为每个可用IP预创建HTTP客户端，添加优化参数
        self.preload_http_clients().await?;
        
        Ok(())
    }

    /// 为每个可用IP预创建HTTP客户端，添加优化参数
    async fn preload_http_clients(&self) -> Result<()> {
        let start_time = Instant::now();
        info!("开始为所有IP地址预创建HTTP客户端...");
        
        let ips = self.available_ips.lock().unwrap().clone();
        let mut clients = self.client_cache.lock().unwrap();
        
        for ip in ips {
            let client = ClientBuilder::new()
                .local_address(ip)
                .timeout(Duration::from_secs(5))
                .pool_idle_timeout(Duration::from_secs(90)) // 连接在池中保持90秒
                .tcp_keepalive(Duration::from_secs(60))     // TCP保活60秒
                .pool_max_idle_per_host(10)                 // 每个主机最多保持10个空闲连接
                .build()?;
                
            clients.insert(ip, client);
            info!("为IP {} 创建了HTTP客户端", ip);
        }
        
        let duration = start_time.elapsed();
        info!("HTTP客户端预创建完成，耗时: {}ms", duration.as_millis());
        Ok(())
    }

    /// 扫描系统上的所有IPv4地址
    async fn scan_system_ips(&self) -> Result<()> {
        // 使用network_interface库获取所有网络接口
        let network_interfaces = list_afinet_netifas()?;
        
        let mut ips = self.available_ips.lock().unwrap();
        ips.clear();

        for (name, ip) in network_interfaces.iter() {
            // 只收集IPv4且非内部地址
            if ip.is_ipv4() && !ip.is_loopback() {
                ips.push(*ip);
                info!("发现IP地址: {} ({})", ip, name);
            }
        }

        // 在Linux系统上尝试使用命令行获取更多IP
        if cfg!(target_os = "linux") {
            match self.get_linux_ips().await {
                Ok(linux_ips) => {
                    for ip in linux_ips {
                        if !ips.contains(&ip) {
                            ips.push(ip);
                            info!("发现额外IP地址: {} (通过ip命令)", ip);
                        }
                    }
                }
                Err(e) => warn!("无法使用Linux命令获取额外IP地址: {}", e),
            }
        }

        Ok(())
    }

    /// 在Linux系统上使用ip命令获取所有IP地址
    async fn get_linux_ips(&self) -> Result<Vec<IpAddr>> {
        let output = Command::new("sh")
            .arg("-c")
            .arg("ip -4 addr show | grep inet | awk '{print $2}' | cut -d/ -f1")
            .output()
            .await?;

        let stdout = String::from_utf8(output.stdout)?;
        let mut ips = Vec::new();

        for line in stdout.lines() {
            let ip = line.trim();
            if !ip.is_empty() && ip != "127.0.0.1" {
                match ip.parse::<IpAddr>() {
                    Ok(addr) => ips.push(addr),
                    Err(e) => warn!("无法解析IP地址 {}: {}", ip, e),
                }
            }
        }

        Ok(ips)
    }

    /// 使用Jito捆绑包端点并发测试IP连通性
    async fn test_ip_connectivity_to_jito(&self) -> Result<()> {
        info!("开始并发测试IP与Jito端点的连通性...");
        
        let ips = self.available_ips.lock().unwrap().clone();
        info!("将同时测试 {} 个IP地址", ips.len());
        
        // 获取Jito端点列表
        let endpoints = self.jito_endpoints.lock().unwrap().clone();
        let jito_endpoint = if !endpoints.is_empty() {
            // 如果有配置的端点，使用第一个进行测试
            endpoints[0].clone()
        } else {
            // 否则使用环境变量或默认值
            let default_endpoint = std::env::var("JITO_RPC_URL")
                .unwrap_or_else(|_| "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles".to_string());
                
            // 将默认端点添加到端点列表
            let mut jito_endpoints = self.jito_endpoints.lock().unwrap();
            if jito_endpoints.is_empty() {
                jito_endpoints.push(default_endpoint.clone());
            }
            drop(jito_endpoints);
            
            default_endpoint
        };
        
        info!("使用Jito端点 {} 测试IP连通性", jito_endpoint);
        
        // 创建一个简单的JSON-RPC请求用于测试
        let test_request = serde_json::json!({
            "jsonrpc": "2.0",
            "id": "connectivity_test",
            "method": "getHealth", // 使用一个简单的方法，即使报错也能测试连接性
            "params": []
        });
        
        // 创建用于并发测试的任务集合
        let mut tasks = Vec::new();
        let tasks_count = ips.len();
        
        for ip in ips {
            let endpoint = jito_endpoint.clone();
            let request = test_request.clone();
            
            // 为每个IP创建一个测试任务
            let task = async move {
                // 创建绑定到特定IP的HTTP客户端
                let client = match reqwest::Client::builder()
                    .local_address(ip)
                    .timeout(Duration::from_secs(5))
                    .build() {
                        Ok(c) => c,
                        Err(e) => {
                            warn!("❌ 为IP {} 创建HTTP客户端失败: {}", ip, e);
                            return (ip, false, 0);
                        }
                    };
                
                // 尝试连接Jito端点
                let start_time = Instant::now();
                match client.post(&endpoint)
                    .json(&request)
                    .send()
                    .await {
                        Ok(_response) => {
                            let duration = start_time.elapsed();
                            (ip, true, duration.as_millis())
                        },
                        Err(e) => {
                            warn!("❌ IP {} 连接到Jito失败: {}", ip, e);
                            (ip, false, 0)
                        }
                    }
            };
            
            tasks.push(task);
        }
        
        // 并发执行所有任务
        let results = future::join_all(tasks).await;
        
        // 处理测试结果
        let mut connected_ips = Vec::new();
        let mut total_success = 0;
        
        for (ip, success, duration) in results {
            if success {
                info!("✅ IP {} 连接到Jito成功，响应时间: {}ms", ip, duration);
                connected_ips.push(ip);
                total_success += 1;
            }
        }
        
        // 更新可用IP列表
        {
            let mut available_ips = self.available_ips.lock().unwrap();
            *available_ips = connected_ips;
        }
        
        info!("IP连通性测试完成，共测试 {} 个IP，{} 个可用", tasks_count, total_success);
        Ok(())
    }

    /// 获取下一个IP地址（轮询方式）
    pub fn get_next_ip(&self) -> Option<IpAddr> {
        let ips = self.available_ips.lock().unwrap();
        if ips.is_empty() {
            return None;
        }
        
        let mut index = self.current_index.lock().unwrap();
        let ip = ips[*index];
        *index = (*index + 1) % ips.len();
        
        Some(ip)
    }

    /// 获取所有可用的IP地址
    pub fn get_all_ips(&self) -> Vec<IpAddr> {
        let ips = self.available_ips.lock().unwrap();
        ips.clone()
    }
    
    /// 使用轮询IP发送Jito捆绑包
    pub async fn send_jito_bundle(&self, bundle_data: Value, endpoint: &str) -> Result<Value> {
        // 记录整个函数的开始时间
        let start_total = Instant::now();
        
        // 1. 获取下一个IP地址
        let start_get_ip = Instant::now();
        let ip = match self.get_next_ip() {
            Some(addr) => addr,
            None => return Err(anyhow::anyhow!("没有可用的IP地址")),
        };
        let get_ip_time = start_get_ip.elapsed();
        info!("获取IP地址耗时: {}μs", get_ip_time.as_micros());
        
        info!("使用IP {} 发送Jito捆绑包...", ip);
        
        // 2. 从缓存获取绑定到特定IP的HTTP客户端
        let start_get_client = Instant::now();
        let client = {
            let clients = self.client_cache.lock().unwrap();
            match clients.get(&ip) {
                Some(client) => client.clone(),
                None => {
                    // 如果缓存中没有，使用默认客户端
                    warn!("IP {} 的HTTP客户端未在缓存中找到，使用默认客户端", ip);
                    self.default_client.clone()
                }
            }
        };
        let get_client_time = start_get_client.elapsed();
        info!("获取HTTP客户端耗时: {}μs", get_client_time.as_micros());
            
        // 3. 发送请求
        let start_send_request = Instant::now();
        let response = client
            .post(endpoint)
            .json(&bundle_data)
            .header("Connection", "keep-alive") // 显式添加keep-alive头，确保连接被保持
            .send()
            .await;
        let send_request_time = start_send_request.elapsed();
        info!("发送HTTP请求耗时: {}ms", send_request_time.as_millis());
        
        // 检查响应是否成功
        let response = match response {
            Ok(resp) => resp,
            Err(e) => {
                info!("HTTP请求失败耗时: {}ms", start_send_request.elapsed().as_millis());
                return Err(anyhow::anyhow!("发送请求失败: {}", e));
            }
        };
            
        // 4. 解析响应
        let start_parse_response = Instant::now();
        let response_data = response.json::<Value>().await?;
        let parse_response_time = start_parse_response.elapsed();
        info!("解析响应JSON耗时: {}μs", parse_response_time.as_micros());
        
        // 5. 处理响应结果
        let start_process_result = Instant::now();
        if let Some(result) = response_data.get("result") {
            info!("使用IP {} 成功发送捆绑包，bundle id: {}", ip, result);
        } else if let Some(error) = response_data.get("error") {
            info!("使用IP {} 发送失败，错误: {}", ip, error);
        }
        let process_result_time = start_process_result.elapsed();
        info!("处理响应结果耗时: {}μs", process_result_time.as_micros());
        
        // 记录总耗时
        let total_time = start_total.elapsed();
        info!("发送Jito捆绑包总耗时: {}ms", total_time.as_millis());
        
        Ok(response_data)
    }

    /// 设置Jito端点列表
    pub fn set_jito_endpoints(&self, endpoints: Vec<String>) {
        let mut jito_endpoints = self.jito_endpoints.lock().unwrap();
        *jito_endpoints = endpoints;
        
        if !jito_endpoints.is_empty() {
            info!("设置了 {} 个Jito端点:", jito_endpoints.len());
            for (i, endpoint) in jito_endpoints.iter().enumerate() {
                info!("Jito端点 [{}]: {}", i + 1, endpoint);
            }
        }
    }

    /// 获取所有配置的Jito端点
    pub fn get_jito_endpoints(&self) -> Vec<String> {
        let jito_endpoints = self.jito_endpoints.lock().unwrap();
        jito_endpoints.clone()
    }
    
    /// 并发发送捆绑包到所有配置的Jito端点
    pub async fn send_jito_bundles(&self, bundle_data: Value) -> Result<Vec<(String, Value)>> {
        // 记录整个函数的开始时间
        let start_total = Instant::now();
        
        // 1. 获取下一个IP地址
        let start_get_ip = Instant::now();
        let ip = match self.get_next_ip() {
            Some(addr) => addr,
            None => return Err(anyhow::anyhow!("没有可用的IP地址")),
        };
        let get_ip_time = start_get_ip.elapsed();
        info!("获取IP地址耗时: {}μs", get_ip_time.as_micros());
        
        // 2. 获取所有Jito端点
        let endpoints = self.jito_endpoints.lock().unwrap().clone();
        if endpoints.is_empty() {
            return Err(anyhow::anyhow!("没有配置Jito端点"));
        }
        
        info!("使用IP {} 向 {} 个Jito端点并发发送捆绑包...", ip, endpoints.len());
        
        // 3. 从缓存获取绑定到特定IP的HTTP客户端
        let start_get_client = Instant::now();
        let client = {
            let clients = self.client_cache.lock().unwrap();
            match clients.get(&ip) {
                Some(client) => client.clone(),
                None => {
                    // 如果缓存中没有，使用默认客户端
                    warn!("IP {} 的HTTP客户端未在缓存中找到，使用默认客户端", ip);
                    self.default_client.clone()
                }
            }
        };
        let get_client_time = start_get_client.elapsed();
        info!("获取HTTP客户端耗时: {}μs", get_client_time.as_micros());
        
        // 4. 创建发送任务
        let mut tasks = Vec::with_capacity(endpoints.len());
        
        for endpoint in &endpoints {
            let client = client.clone();
            let bundle_data = bundle_data.clone();
            let endpoint_clone = endpoint.clone();
            
            let task = async move {
                let start_send = Instant::now();
                
                // 发送请求
                let response = client
                    .post(&endpoint_clone)
                    .json(&bundle_data)
                    .send()
                    .await;
                    
                let duration = start_send.elapsed();
                
                match response {
                    Ok(resp) => {
                        // 解析响应
                        match resp.json::<Value>().await {
                            Ok(data) => {
                                if let Some(result) = data.get("result") {
                                    info!("向端点 {} 发送成功，bundle id: {}，耗时: {}ms", 
                                          endpoint_clone, result, duration.as_millis());
                                } else if let Some(error) = data.get("error") {
                                    info!("向端点 {} 发送失败，错误: {}，耗时: {}ms", 
                                          endpoint_clone, error, duration.as_millis());
                                }
                                
                                (endpoint_clone, data)
                            },
                            Err(e) => {
                                info!("解析端点 {} 响应失败: {}，耗时: {}ms", 
                                      endpoint_clone, e, duration.as_millis());
                                (endpoint_clone, serde_json::json!({"error": format!("解析响应失败: {}", e)}))
                            }
                        }
                    },
                    Err(e) => {
                        info!("向端点 {} 发送请求失败: {}，耗时: {}ms",
                              endpoint_clone, e, duration.as_millis());
                        (endpoint_clone, serde_json::json!({"error": format!("发送请求失败: {}", e)}))
                    }
                }
            };
            
            tasks.push(task);
        }
        
        // 5. 并发执行所有任务
        let results = future::join_all(tasks).await;
        
        // 记录总耗时
        let total_time = start_total.elapsed();
        info!("向 {} 个Jito端点并发发送捆绑包总耗时: {}ms", endpoints.len(), total_time.as_millis());
        
        Ok(results)
    }
}

impl Default for IpRotator {
    fn default() -> Self {
        Self::new()
    }
} 