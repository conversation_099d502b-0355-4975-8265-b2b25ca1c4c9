use solana_program::instruction::{AccountMeta, Instruction};
use solana_program::pubkey::Pubkey;
use solana_program::sysvar;
use std::str::FromStr;
use crate::consts::{WSOL_MINT, USDC_MINT};

pub const KAMINO_LENDING_PROGRAM_ID: &str = "KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD";

pub const KAMINO_SOL_LENDING_MARKET_AUTHORITY: &str = "Dx8iy2o46sK1DzWbEcznqSKeLbLVeu7otkibA3WohGAj";
pub const KAMINO_SOL_LENDING_MARKET: &str = "H6rHXmXoCQvq8Ue81MqNh7ow5ysPa1dSozwW3PU1dDH6";

pub const KAMINO_SOL_RESERVE: &str = "6gTJfuPHEg6uRAijRkMqNc9kan4sVZejKMxmvx2grT1p";
pub const KAMINO_SOL_RESERVE_LIQUIDITY: &str = "ywaaLvG7t1vXJo8sT3UzE8yzzZtxLM7Fmev64Jbooye";
pub const KAMINO_SOL_FEE_RECEIVER: &str = "EQ7hw63aBS7aPQqXsoxaaBxiwbEzaAiY9Js6tCekkqxf";

pub const KAMINO_USDC_LENDING_MARKET_AUTHORITY: &str = "9DrvZvyWh1HuAoZxvYWMvkf2XCzryCpGgHqrMjyDWpmo";
pub const KAMINO_USDC_LENDING_MARKET: &str = "7u3HeHxYDLhnCoErrtycNokbQYbWGzLs6JSDqGAv5PfF";

pub const KAMINO_USDC_RESERVE: &str = "D6q6wuQSrifJKZYpR1M8R4YawnLDtDsMmWM1NbBmgJ59";
pub const KAMINO_USDC_RESERVE_LIQUIDITY: &str = "Bgq7trRgVMeq33yt235zM2onQ4bRDBsY5EWiTetF4qw6";
pub const KAMINO_USDC_FEE_RECEIVER: &str = "BbDUrk1bVtSixgQsPLBJFZEF7mwGstnD5joA1WzYvYFX";

pub const KAMINO_REFERRER_TOKEN_STATE: &str = "KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD";
pub const KAMINO_REFERRER_ACCOUNT: &str = "KLend2g3cP87fffoy8q1mQqGKjrxjC8boSyAYavgmjD";

pub const KAMINO_ADDITIONAL_COMPUTE_UNITS: u32 = 80_000;

// 闪电贷手续费率：0.00001 (十万分之一)
pub const KAMINO_FLASHLOAN_FEE_RATE: f64 = 0.00001;

pub struct FlashBorrowReserveLiquidity;

impl FlashBorrowReserveLiquidity {
    pub fn instruction_data(amount: u64) -> Vec<u8> {
        let mut data = vec![135, 231, 52, 167, 7, 52, 212, 193]; // Anchor discriminator for flashBorrowReserveLiquidity
        data.extend_from_slice(&amount.to_le_bytes());
        data
    }
}

pub struct FlashRepayReserveLiquidity;

impl FlashRepayReserveLiquidity {
    pub fn instruction_data(amount: u64, borrow_instruction_index: u8) -> Vec<u8> {
        let mut data = vec![185, 117, 0, 203, 96, 245, 180, 186]; // Anchor discriminator for flashRepayReserveLiquidity
        data.extend_from_slice(&amount.to_le_bytes());
        data.push(borrow_instruction_index);
        data
    }
}

pub fn get_sol_kamino_flashloan_borrow_ix(
    wallet_pk: &Pubkey,
    destination_token_account: Pubkey,
    lending_amount: u64,
) -> anyhow::Result<Instruction> {
    let kamino_program_id = Pubkey::from_str(KAMINO_LENDING_PROGRAM_ID)?;
    let lending_market_authority = Pubkey::from_str(KAMINO_SOL_LENDING_MARKET_AUTHORITY)?;
    let lending_market = Pubkey::from_str(KAMINO_SOL_LENDING_MARKET)?;
    let reserve = Pubkey::from_str(KAMINO_SOL_RESERVE)?;
    let reserve_liquidity_mint = Pubkey::from_str(WSOL_MINT)?;
    let reserve_source_liquidity = Pubkey::from_str(KAMINO_SOL_RESERVE_LIQUIDITY)?;
    let fee_receiver = Pubkey::from_str(KAMINO_SOL_FEE_RECEIVER)?;
    let referrer_token_state = Pubkey::from_str(KAMINO_REFERRER_TOKEN_STATE)?;
    let referrer_account = Pubkey::from_str(KAMINO_REFERRER_ACCOUNT)?;

    let accounts = vec![
        AccountMeta::new(*wallet_pk, true), // userTransferAuthority
        AccountMeta::new_readonly(lending_market_authority, false), // lendingMarketAuthority
        AccountMeta::new_readonly(lending_market, false), // lendingMarket
        AccountMeta::new(reserve, false),   // reserve
        AccountMeta::new_readonly(reserve_liquidity_mint, false), // reserveLiquidityMint
        AccountMeta::new(reserve_source_liquidity, false), // reserveSourceLiquidity
        AccountMeta::new(destination_token_account, false), // userDestinationLiquidity
        AccountMeta::new(fee_receiver, false), // reserveLiquidityFeeReceiver
        AccountMeta::new_readonly(referrer_token_state, false), // referrerTokenState
        AccountMeta::new_readonly(referrer_account, false), // referrerAccount
        AccountMeta::new_readonly(sysvar::instructions::id(), false), // sysvarInfo
        AccountMeta::new_readonly(spl_token::id(), false), // tokenProgram
    ];

    Ok(Instruction {
        program_id: kamino_program_id,
        accounts,
        data: FlashBorrowReserveLiquidity::instruction_data(lending_amount),
    })
}

pub fn get_sol_kamino_flashloan_repay_ix(
    wallet_pk: &Pubkey,
    source_token_account: Pubkey,
    lending_amount: u64,
    borrow_instruction_index: u8,
) -> anyhow::Result<Instruction> {
    let kamino_program_id = Pubkey::from_str(KAMINO_LENDING_PROGRAM_ID)?;
    let lending_market_authority = Pubkey::from_str(KAMINO_SOL_LENDING_MARKET_AUTHORITY)?;
    let lending_market = Pubkey::from_str(KAMINO_SOL_LENDING_MARKET)?;
    let reserve = Pubkey::from_str(KAMINO_SOL_RESERVE)?;
    let reserve_liquidity_mint = Pubkey::from_str(WSOL_MINT)?;
    let reserve_destination_liquidity = Pubkey::from_str(KAMINO_SOL_RESERVE_LIQUIDITY)?;
    let fee_receiver = Pubkey::from_str(KAMINO_SOL_FEE_RECEIVER)?;
    let referrer_token_state = Pubkey::from_str(KAMINO_REFERRER_TOKEN_STATE)?;
    let referrer_account = Pubkey::from_str(KAMINO_REFERRER_ACCOUNT)?;

    let accounts = vec![
        AccountMeta::new(*wallet_pk, true), // userTransferAuthority
        AccountMeta::new_readonly(lending_market_authority, false), // lendingMarketAuthority
        AccountMeta::new_readonly(lending_market, false), // lendingMarket
        AccountMeta::new(reserve, false),   // reserve
        AccountMeta::new_readonly(reserve_liquidity_mint, false), // reserveLiquidityMint
        AccountMeta::new(reserve_destination_liquidity, false), // reserveDestinationLiquidity
        AccountMeta::new(source_token_account, false), // userSourceLiquidity
        AccountMeta::new(fee_receiver, false), // reserveLiquidityFeeReceiver
        AccountMeta::new_readonly(referrer_token_state, false), // referrerTokenState
        AccountMeta::new_readonly(referrer_account, false), // referrerAccount
        AccountMeta::new_readonly(sysvar::instructions::id(), false), // sysvarInfo
        AccountMeta::new_readonly(spl_token::id(), false), // tokenProgram
    ];

    Ok(Instruction {
        program_id: kamino_program_id,
        accounts,
        data: FlashRepayReserveLiquidity::instruction_data(
            lending_amount,
            borrow_instruction_index,
        ),
    })
}

pub fn get_usdc_kamino_flashloan_borrow_ix(
    wallet_pk: &Pubkey,
    destination_token_account: Pubkey,
    lending_amount: u64,
) -> anyhow::Result<Instruction> {
    let kamino_program_id = Pubkey::from_str(KAMINO_LENDING_PROGRAM_ID)?;
    let lending_market_authority = Pubkey::from_str(KAMINO_USDC_LENDING_MARKET_AUTHORITY)?;
    let lending_market = Pubkey::from_str(KAMINO_USDC_LENDING_MARKET)?;
    let reserve = Pubkey::from_str(KAMINO_USDC_RESERVE)?;
    let reserve_liquidity_mint = Pubkey::from_str(USDC_MINT)?;
    let reserve_source_liquidity = Pubkey::from_str(KAMINO_USDC_RESERVE_LIQUIDITY)?;
    let fee_receiver = Pubkey::from_str(KAMINO_USDC_FEE_RECEIVER)?;
    let referrer_token_state = Pubkey::from_str(KAMINO_REFERRER_TOKEN_STATE)?;
    let referrer_account = Pubkey::from_str(KAMINO_REFERRER_ACCOUNT)?;

    let accounts = vec![
        AccountMeta::new(*wallet_pk, true), // userTransferAuthority
        AccountMeta::new_readonly(lending_market_authority, false), // lendingMarketAuthority
        AccountMeta::new_readonly(lending_market, false), // lendingMarket
        AccountMeta::new(reserve, false),   // reserve
        AccountMeta::new_readonly(reserve_liquidity_mint, false), // reserveLiquidityMint
        AccountMeta::new(reserve_source_liquidity, false), // reserveSourceLiquidity
        AccountMeta::new(destination_token_account, false), // userDestinationLiquidity
        AccountMeta::new(fee_receiver, false), // reserveLiquidityFeeReceiver
        AccountMeta::new_readonly(referrer_token_state, false), // referrerTokenState
        AccountMeta::new_readonly(referrer_account, false), // referrerAccount
        AccountMeta::new_readonly(sysvar::instructions::id(), false), // sysvarInfo
        AccountMeta::new_readonly(spl_token::id(), false), // tokenProgram
    ];

    Ok(Instruction {
        program_id: kamino_program_id,
        accounts,
        data: FlashBorrowReserveLiquidity::instruction_data(lending_amount),
    })
}

pub fn get_usdc_kamino_flashloan_repay_ix(
    wallet_pk: &Pubkey,
    source_token_account: Pubkey,
    lending_amount: u64,
    borrow_instruction_index: u8,
) -> anyhow::Result<Instruction> {
    let kamino_program_id = Pubkey::from_str(KAMINO_LENDING_PROGRAM_ID)?;
    let lending_market_authority = Pubkey::from_str(KAMINO_USDC_LENDING_MARKET_AUTHORITY)?;
    let lending_market = Pubkey::from_str(KAMINO_USDC_LENDING_MARKET)?;
    let reserve = Pubkey::from_str(KAMINO_USDC_RESERVE)?;
    let reserve_liquidity_mint = Pubkey::from_str(USDC_MINT)?;
    let reserve_destination_liquidity = Pubkey::from_str(KAMINO_USDC_RESERVE_LIQUIDITY)?;
    let fee_receiver = Pubkey::from_str(KAMINO_USDC_FEE_RECEIVER)?;
    let referrer_token_state = Pubkey::from_str(KAMINO_REFERRER_TOKEN_STATE)?;
    let referrer_account = Pubkey::from_str(KAMINO_REFERRER_ACCOUNT)?;

    let accounts = vec![
        AccountMeta::new(*wallet_pk, true), // userTransferAuthority
        AccountMeta::new_readonly(lending_market_authority, false), // lendingMarketAuthority
        AccountMeta::new_readonly(lending_market, false), // lendingMarket
        AccountMeta::new(reserve, false),   // reserve
        AccountMeta::new_readonly(reserve_liquidity_mint, false), // reserveLiquidityMint
        AccountMeta::new(reserve_destination_liquidity, false), // reserveDestinationLiquidity
        AccountMeta::new(source_token_account, false), // userSourceLiquidity
        AccountMeta::new(fee_receiver, false), // reserveLiquidityFeeReceiver
        AccountMeta::new_readonly(referrer_token_state, false), // referrerTokenState
        AccountMeta::new_readonly(referrer_account, false), // referrerAccount
        AccountMeta::new_readonly(sysvar::instructions::id(), false), // sysvarInfo
        AccountMeta::new_readonly(spl_token::id(), false), // tokenProgram
    ];

    Ok(Instruction {
        program_id: kamino_program_id,
        accounts,
        data: FlashRepayReserveLiquidity::instruction_data(
            lending_amount,
            borrow_instruction_index,
        ),
    })
}

/// 计算闪电贷手续费（lamports）
pub fn calculate_flashloan_fee_lamports(
    lending_amount: u64,
    is_wsol: bool,
    price_cache_manager: &crate::price_cache_manager::PriceCacheManager
) -> u64 {
    let fee_amount = (lending_amount as f64 * KAMINO_FLASHLOAN_FEE_RATE) as u64;

    if is_wsol {
        // WSOL：直接返回lamports
        fee_amount
    } else {
        // USDC：转换为lamports
        price_cache_manager.convert_usdc_profit_to_sol_lamports(fee_amount)
    }
}
