use anyhow::{Context, Result};
use log::{error, info};
use solana_sdk::{
    signature::Keypair,
    signer::Signer,
};
use std::{
    fs,
    path::{Path, PathBuf},
    sync::{Arc, Mutex},
};

/// Jito小费账户管理器
pub struct JitoTipManager {
    tip_accounts: Arc<Mutex<Vec<Keypair>>>,
    current_index: Arc<Mutex<usize>>,
    tip_accounts_file_path: PathBuf,
    tip_accounts_count: usize,
}

impl JitoTipManager {
    /// 创建新的Jito小费管理器
    pub fn new(tip_accounts_count: usize, data_dir: &Path) -> Result<Self> {
        let tip_accounts_file_path = data_dir.join("jito_tip_accounts.json");
        
        let manager = Self {
            tip_accounts: Arc::new(Mutex::new(Vec::new())),
            current_index: Arc::new(Mutex::new(0)),
            tip_accounts_file_path,
            tip_accounts_count,
        };
        
        // 加载或创建小费账户
        if let Err(e) = manager.load_or_create_tip_accounts() {
            error!("加载小费账户时出错: {}", e);
        }
        
        Ok(manager)
    }
    
    /// 加载或创建小费账户
    fn load_or_create_tip_accounts(&self) -> Result<()> {
        if self.tip_accounts_file_path.exists() {
            self.load_tip_accounts()?;
        } else {
            self.create_tip_accounts()?;
        }
        
        Ok(())
    }
    
    /// 从文件加载小费账户
    fn load_tip_accounts(&self) -> Result<()> {
        let file_content = fs::read_to_string(&self.tip_accounts_file_path)
            .context("读取小费账户文件失败")?;
            
        let keypair_bytes: Vec<Vec<u8>> = serde_json::from_str(&file_content)
            .context("解析小费账户JSON失败")?;
            
        let mut keypairs = Vec::with_capacity(keypair_bytes.len());
        
        for bytes in keypair_bytes {
            let keypair = Keypair::from_bytes(&bytes)
                .context("从字节构建密钥对失败")?;
                
            keypairs.push(keypair);
        }
        
        let mut tip_accounts = self.tip_accounts.lock().unwrap();
        *tip_accounts = keypairs;
        
        info!("已加载 {} 个Jito小费账户", tip_accounts.len());
        Ok(())
    }
    
    /// 创建新的小费账户
    fn create_tip_accounts(&self) -> Result<()> {
        info!("创建 {} 个新的Jito小费账户...", self.tip_accounts_count);
        
        // 创建数据目录（如果不存在）
        if let Some(parent) = self.tip_accounts_file_path.parent() {
            fs::create_dir_all(parent)?;
        }
        
        let mut keypairs = Vec::with_capacity(self.tip_accounts_count);
        for _ in 0..self.tip_accounts_count {
            keypairs.push(Keypair::new());
        }
        
        // 将密钥保存到文件
        let keypair_bytes: Vec<Vec<u8>> = keypairs.iter()
            .map(|kp| kp.to_bytes().to_vec())
            .collect();
            
        let json = serde_json::to_string_pretty(&keypair_bytes)?;
        fs::write(&self.tip_accounts_file_path, json)?;
        
        let mut tip_accounts = self.tip_accounts.lock().unwrap();
        *tip_accounts = keypairs;
        
        info!("已创建并保存 {} 个Jito小费账户", self.tip_accounts_count);
        Ok(())
    }
    
    /// 获取下一个小费账户
    pub fn get_next_tip_account(&self) -> Keypair {
        let accounts = self.tip_accounts.lock().unwrap();
        if accounts.is_empty() {
            // 如果没有账户，创建一个新的
            return Keypair::new();
        }
        
        let mut index = self.current_index.lock().unwrap();
        let account_index = *index;
        *index = (*index + 1) % accounts.len();
        
        // 克隆Keypair以返回
        let keypair_bytes = accounts[account_index].to_bytes();
        Keypair::from_bytes(&keypair_bytes).unwrap()
    }
    
    /// 获取所有小费账户的公钥
    pub fn get_all_pubkeys(&self) -> Vec<String> {
        let accounts = self.tip_accounts.lock().unwrap();
        accounts.iter()
            .map(|kp| kp.pubkey().to_string())
            .collect()
    }
} 