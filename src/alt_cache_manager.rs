use anyhow::{Context, Result};
use log::{error, info};
use solana_program::pubkey::Pubkey;
use solana_client::rpc_client::RpcClient;
use solana_program::address_lookup_table::state::AddressLookupTable;
use solana_sdk::address_lookup_table_account::AddressLookupTableAccount;
use std::{
    collections::HashMap,
    fs,
    path::{Path, PathBuf},
    str::FromStr,
    sync::{Arc, Mutex},
};

pub struct AltCacheManager {
    alt_cache: Arc<Mutex<HashMap<String, AddressLookupTableAccount>>>,
    cache_file_path: PathBuf,
    client: Arc<RpcClient>,
}

impl AltCacheManager {
    /// 创建新的ALT缓存管理器
    pub fn new(client: Arc<RpcClient>, data_dir: &Path) -> Result<Self> {
        let cache_file_path = data_dir.join("alt_cache.json");
        
        let manager = Self {
            alt_cache: Arc::new(Mutex::new(HashMap::new())),
            cache_file_path,
            client,
        };
        
        // 加载缓存（如果存在）
        if let Err(e) = manager.load_from_file() {
            error!("加载ALT缓存时出错: {}", e);
        }
        
        Ok(manager)
    }
    
    /// 从文件加载缓存
    fn load_from_file(&self) -> Result<()> {
        if !self.cache_file_path.exists() {
            info!("ALT缓存文件不存在，将创建新缓存");
            return Ok(());
        }
        
        let file_content = fs::read_to_string(&self.cache_file_path)
            .context("读取ALT缓存文件失败")?;
            
        let cache_data: HashMap<String, CachedAltAccount> = serde_json::from_str(&file_content)
            .context("解析ALT缓存JSON失败")?;
            
        let mut alt_cache = self.alt_cache.lock().unwrap();
        
        for (address, account_data) in cache_data {
            let account = self.deserialize_alt_account(&address, &account_data)?;
            alt_cache.insert(address, account);
        }
        
        info!("已从文件加载 {} 个ALT缓存", alt_cache.len());
        Ok(())
    }
    
    /// 保存缓存到文件
    pub fn save_to_file(&self) -> Result<()> {
        let alt_cache = self.alt_cache.lock().unwrap();
        
        let mut cache_data = HashMap::new();
        for (address, account) in alt_cache.iter() {
            cache_data.insert(address.clone(), self.serialize_alt_account(account)?);
        }
        
        // 创建目录（如果不存在）
        if let Some(parent) = self.cache_file_path.parent() {
            fs::create_dir_all(parent)?;
        }
        
        let json = serde_json::to_string_pretty(&cache_data)?;
        fs::write(&self.cache_file_path, json)?;
        
        info!("已保存 {} 个ALT缓存到文件", alt_cache.len());
        Ok(())
    }
    
    /// 序列化ALT账户以便保存到文件
    fn serialize_alt_account(&self, account: &AddressLookupTableAccount) -> Result<CachedAltAccount> {
        Ok(CachedAltAccount {
            addresses: account.addresses.iter().map(|addr| addr.to_string()).collect(),
        })
    }
    
    /// 从序列化数据恢复ALT账户
    fn deserialize_alt_account(&self, address: &str, data: &CachedAltAccount) -> Result<AddressLookupTableAccount> {
        let addresses = data.addresses.iter()
            .map(|addr| Pubkey::from_str(addr).context(format!("解析地址失败: {}", addr)))
            .collect::<Result<Vec<_>>>()?;
            
        Ok(AddressLookupTableAccount {
            key: Pubkey::from_str(address)?,
            addresses,
        })
    }
    
    /// 从文件加载ALT地址列表并预热缓存
    pub async fn initialize_from_address_list(&self, address_list_file: &Path) -> Result<()> {
        if !address_list_file.exists() {
            error!("地址列表文件不存在: {:?}", address_list_file);
            return Ok(());
        }
        
        let content = fs::read_to_string(address_list_file)?;
        let addresses: Vec<String> = content.lines()
            .map(|line| line.trim().to_string())
            .filter(|line| !line.is_empty() && line.len() > 30) // 简单过滤有效的地址
            .collect();
            
        let addresses_count = addresses.len();
        info!("从文件加载了 {} 个ALT地址，开始预热缓存...", addresses_count);
        
        let mut loaded = 0;
        let mut alt_cache = self.alt_cache.lock().unwrap();
        
        for address in addresses {
            if !alt_cache.contains_key(&address) {
                match self.load_alt_from_rpc(&address).await {
                    Ok(alt_account) => {
                        alt_cache.insert(address.clone(), alt_account);
                        loaded += 1;
                        info!("已缓存ALT({}/{}): {}", loaded, addresses_count, address);
                    }
                    Err(e) => {
                        error!("加载ALT失败 {}: {}", address, e);
                    }
                }
            }
        }
        
        info!("ALT缓存预热完成，共加载 {} 个地址查找表", loaded);
        drop(alt_cache);
        self.save_to_file()?;
        
        Ok(())
    }
    
    /// 通过RPC加载ALT账户
    async fn load_alt_from_rpc(&self, address: &str) -> Result<AddressLookupTableAccount> {
        let pubkey = Pubkey::from_str(address)?;
        let raw_account = self.client.get_account(&pubkey)?;
        let address_lookup_table = AddressLookupTable::deserialize(&raw_account.data)?;
        
        Ok(AddressLookupTableAccount {
            key: pubkey,
            addresses: address_lookup_table.addresses.to_vec(),
        })
    }
    
    /// 获取ALT缓存
    pub fn get_alt_account(&self, address: &str) -> Option<AddressLookupTableAccount> {
        let alt_cache = self.alt_cache.lock().unwrap();
        alt_cache.get(address).cloned()
    }
    
    /// 获取或加载ALT账户
    pub async fn get_or_load_alt_account(&self, address: &str) -> Result<AddressLookupTableAccount> {
        // 检查缓存
        if let Some(cached) = self.get_alt_account(address) {
            return Ok(cached);
        }
        
        // 缓存未命中，通过RPC加载
        info!("缓存未命中，通过RPC加载ALT: {}", address);
        let alt_account = self.load_alt_from_rpc(address).await?;
        
        // 更新缓存
        let mut alt_cache = self.alt_cache.lock().unwrap();
        alt_cache.insert(address.to_string(), alt_account.clone());
        drop(alt_cache);
        
        // 保存更新后的缓存
        self.save_to_file()?;
        
        Ok(alt_account)
    }
    
    /// 从地址列表获取ALT账户
    pub async fn get_address_lookup_table_accounts(&self, addresses: &[String]) -> Result<Vec<AddressLookupTableAccount>> {
        let mut accounts = Vec::with_capacity(addresses.len());
        
        for address in addresses {
            match self.get_or_load_alt_account(address).await {
                Ok(account) => {
                    accounts.push(account);
                }
                Err(e) => {
                    error!("获取ALT账户失败 {}: {}", address, e);
                }
            }
        }
        
        Ok(accounts)
    }
}

/// 缓存的ALT账户数据结构（用于序列化/反序列化）
#[derive(serde::Serialize, serde::Deserialize)]
struct CachedAltAccount {
    addresses: Vec<String>,
} 