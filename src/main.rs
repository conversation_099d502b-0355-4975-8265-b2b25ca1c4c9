use anyhow::Result;
use bot::ArbitrageBot;
use env_logger::Builder;
use log::{LevelFilter, info, error};
use std::io::Write;
use std::path::Path;
use std::sync::Arc;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;

mod bot;
mod consts;
mod types;
mod config;
mod jito_tip_manager;
mod alt_cache_manager;
mod blockhash_cache_manager;
mod price_cache_manager;
mod IpRotator;
mod ProxyRotator;
mod kamino;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志记录器，添加时间戳
    Builder::new()
        .format(|buf, record| {
            writeln!(
                buf,
                "{} [{}] {}",
                chrono::Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.args()
            )
        })
        .filter(None, LevelFilter::Info)
        .init();

    // 加载环境变量
    dotenv::dotenv().ok();

    // 配置文件路径
    let config_path = Path::new("config.yaml");
    
    // 创建并初始化机器人
    info!("正在创建多代币对套利机器人...");
    let bot = Arc::new(ArbitrageBot::new(config_path)?);
    
    // 初始化发送器（根据配置选择IP轮询或代理）
    info!("开始初始化发送器...");
    if let Err(e) = bot.initialize_sender().await {
        error!("初始化发送器失败: {}", e);
    }
    
    // 初始化区块哈希缓存
    info!("开始初始化区块哈希缓存...");
    if let Err(e) = bot.initialize_blockhash_cache().await {
        error!("初始化区块哈希缓存失败: {}", e);
    }
    
    // 初始化价格缓存管理器
    info!("开始初始化价格缓存管理器...");
    if let Err(e) = bot.initialize_price_cache().await {
        error!("初始化价格缓存管理器失败: {}", e);
    }
    
    // 预热 ALT 缓存
    info!("开始预热 ALT 缓存...");
    if let Err(e) = bot.preload_alt_cache().await {
        error!("预热 ALT 缓存失败: {}", e);
    }

    // 获取有效的套利代币对
    let config = bot.get_config_manager().get_config();
    let arb_pairs = bot.get_config_manager().get_valid_arb_pairs();
    
    info!("已加载 {} 个有效套利代币对", arb_pairs.len());
    
    // 为每个套利对创建异步任务
    let mut task_handles: Vec<JoinHandle<()>> = Vec::new();
    
    for pair in arb_pairs {
        let bot_clone = bot.clone();
        let pair_name = pair.name.clone().unwrap_or_else(|| 
            format!("{}->{}", pair.input_mint, pair.output_mint));
        
        // 获取该套利对的轮询间隔，如果没有设置则使用全局间隔
        let poll_interval_ms = pair.poll_interval_ms.unwrap_or(config.poll_interval_ms);
        
        info!("启动套利对 [{}] 监控，轮询间隔: {}ms", pair_name, poll_interval_ms);
        
        // 🚀 关键修改：实现真正的高并发套利
        let handle = tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_millis(poll_interval_ms));
            
            loop {
                interval.tick().await;
                
                // 🎯 核心改进：为每次套利检查创建独立的异步任务
                // 这样定时器不会被阻塞，实现真正的并发执行
                let bot_shared = bot_clone.clone();
                let pair_clone = pair.clone();
                let pair_name_clone = pair_name.clone();
                
                tokio::spawn(async move {
                    if let Err(e) = bot_shared.check_arbitrage_opportunity(&pair_clone).await {
                        error!("套利对 [{}] 检查错误: {}", pair_name_clone, e);
                    }
                });
            }
        });
        
        task_handles.push(handle);
    }
    
    info!("所有套利代币对监控任务已启动，等待执行...");

    // 等待所有任务完成（实际上它们会一直运行）
    for handle in task_handles {
        if let Err(e) = handle.await {
            error!("套利任务异常终止: {}", e);
        }
    }

    Ok(())
}