[package]
name = "sol-arb-bot"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.36", features = ["full"] }
solana-sdk = "1.18"
solana-client = "1.18"
solana-program = "1.18"
reqwest = { version = "0.11", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
bs58 = "0.4.0"
base64 = "0.21"
bincode = "1.3.3"
dotenv = "0.15"
solana-address-lookup-table-program = "1.18"
futures = "0.3"
lazy_static = "1.4"
log = "0.4"
env_logger = "0.10"
chrono = "0.4"
rand = "0.8"
local-ip-address = "0.5.1"
serde_yaml = "0.8"
spl-token = "4.0"
