{"rustc": 11410426090777951712, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 15657897354478470176, "path": 8636499643481877149, "deps": [[233996378138764347, "flate2", false, 1874172761892404224], [2877347214279964928, "pin_project_lite", false, 11655009362744776504], [3107892209185275978, "tokio", false, 11824331570122658924], [3129130049864710036, "memchr", false, 11710378647360533631], [7620660491849607393, "futures_core", false, 14709588967569401042], [14132538657330703225, "brotli", false, 181855420208792331]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-e1aec00576fda45d/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}