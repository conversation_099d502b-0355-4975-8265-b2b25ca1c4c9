{"rustc": 11410426090777951712, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2891665871583465023, "build_script_build", false, 1250860780807110201]], "local": [{"RerunIfChanged": {"output": "debug/build/blake3-5faeff78ead357b0/output", "paths": ["c/blake3.h", "c/blake3_neon.c", "c/blake3-config.cmake.in", "c/blake3_sse2_x86-64_windows_msvc.asm", "c/blake3.c", "c/blake3_sse41.c", "c/blake3_dispatch.c", "c/blake3_avx2_x86-64_unix.S", "c/blake3_avx512_x86-64_windows_msvc.asm", "c/CMakeLists.txt", "c/main.c", "c/libblake3.pc.in", "c/blake3_avx2_x86-64_windows_msvc.asm", "c/blake3_sse41_x86-64_unix.S", "c/blake3_avx512_x86-64_unix.S", "c/CMakePresets.json", "c/blake3_avx512.c", "c/blake3_impl.h", "c/README.md", "c/blake3_sse2.c", "c/example.c", "c/blake3_sse2_x86-64_windows_gnu.S", "c/blake3_sse41_x86-64_windows_msvc.asm", "c/test.py", "c/.giti<PERSON>re", "c/blake3_avx2_x86-64_windows_gnu.S", "c/blake3_avx512_x86-64_windows_gnu.S", "c/blake3_portable.c", "c/blake3_sse41_x86-64_windows_gnu.S", "c/blake3_sse2_x86-64_unix.S", "c/Makefile.testing", "c/blake3_avx2.c"]}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PREFER_INTRINSICS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_NO_NEON", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_PURE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}