{"rustc": 11410426090777951712, "features": "[\"atty\", \"cargo\", \"color\", \"default\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 2241668132362809309, "path": 4360627559389596061, "deps": [[5070769681332304831, "once_cell", false, 10353949076335221885], [5841926810058920975, "strsim", false, 2208504348163178535], [10058577953979766589, "atty", false, 17935376274079002598], [10435729446543529114, "bitflags", false, 18188691524635306757], [12902659978838094914, "termcolor", false, 18206991578232065943], [13685676330757124427, "textwrap", false, 11465057569173902615], [14923790796823607459, "indexmap", false, 545272194297983724], [15944592714770878610, "clap_lex", false, 14460737546774820304]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-218015542a690427/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}