{"rustc": 11410426090777951712, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 2241668132362809309, "path": 6138561264400601437, "deps": [[2932480923465029663, "zeroize", false, 12065852831587227268], [4731167174326621189, "rand", false, 7963928887771625902], [9431183304631869056, "curve25519_dalek", false, 12696996411404628705], [10967960060725374459, "serde_crate", false, 1794299219513741103], [11472355562936271783, "sha2", false, 5945305129116902005], [16629266738323756185, "ed25519", false, 5888362303912208109]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-3de41dad0121bfeb/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}