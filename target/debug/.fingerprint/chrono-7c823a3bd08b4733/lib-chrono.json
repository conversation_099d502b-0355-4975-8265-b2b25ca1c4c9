{"rustc": 11410426090777951712, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "target": 15315924755136109342, "profile": 2241668132362809309, "path": 1666785929861479241, "deps": [[2133346516990077175, "iana_time_zone", false, 8800589502010308018], [5157631553186200874, "num_traits", false, 8523267759794121359], [10967960060725374459, "serde", false, 1794299219513741103]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-7c823a3bd08b4733/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}