{"rustc": 11410426090777951712, "features": "[\"default\", \"schema\"]", "declared_features": "[\"default\", \"force_exhaustive_checks\", \"schema\"]", "target": 18019366223131144178, "profile": 2225463790103693989, "path": 8229949569385175676, "deps": [[1133817422595695190, "proc_macro2", false, 1757359286263230981], [4078389535049962916, "proc_macro_crate", false, 2784339069097102392], [5070769681332304831, "once_cell", false, 8348696261388979316], [13649996897799714854, "syn", false, 18119111562169631226], [14165535970700196836, "quote", false, 9990653686269456406]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/borsh-derive-3b5b7928f70f3419/dep-lib-borsh_derive", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}