{"rustc": 11410426090777951712, "features": "[\"dummy\", \"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 2241668132362809309, "path": 10371856813955477107, "deps": [[2924422107542798392, "libc", false, 16514330017800470075], [5170503507811329045, "build_script_build", false, 12377097025811912770], [10411997081178400487, "cfg_if", false, 18134666949081840738]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-0b8ea9372aadf980/dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}