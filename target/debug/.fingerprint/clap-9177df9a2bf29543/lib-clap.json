{"rustc": 11410426090777951712, "features": "[\"ansi_term\", \"atty\", \"color\", \"default\", \"strsim\", \"suggestions\", \"vec_map\"]", "declared_features": "[\"ansi_term\", \"atty\", \"clippy\", \"color\", \"debug\", \"default\", \"doc\", \"nightly\", \"no_cargo\", \"strsim\", \"suggestions\", \"term_size\", \"unstable\", \"vec_map\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 12198692761336931930, "profile": 15657897354478470176, "path": 618277348759997503, "deps": [[1322514204948454048, "unicode_width", false, 5900207338388026115], [1810510990979880151, "ansi_term", false, 13661475118430126825], [6485010074357387197, "textwrap", false, 11409577861927368831], [10058577953979766589, "atty", false, 14446544102526546006], [10110425334065384495, "strsim", false, 6375761732086913049], [10435729446543529114, "bitflags", false, 14830140219543786391], [14451951854123638585, "vec_map", false, 16637484655632180506]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-9177df9a2bf29543/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}