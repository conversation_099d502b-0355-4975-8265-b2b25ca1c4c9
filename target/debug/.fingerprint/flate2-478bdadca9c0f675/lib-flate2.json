{"rustc": 11410426090777951712, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 12064207476526245164, "deps": [[2950323987832879288, "miniz_oxide", false, 3648802250584032203], [5466618496199522463, "crc32fast", false, 7770655557196996952]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-478bdadca9c0f675/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}