{"rustc": 11410426090777951712, "features": "[\"atty\", \"cargo\", \"color\", \"default\", \"once_cell\", \"std\", \"strsim\", \"suggestions\", \"termcolor\"]", "declared_features": "[\"atty\", \"backtrace\", \"cargo\", \"clap_derive\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"once_cell\", \"regex\", \"std\", \"strsim\", \"suggestions\", \"termcolor\", \"terminal_size\", \"unicase\", \"unicode\", \"unstable-doc\", \"unstable-grouped\", \"unstable-replace\", \"unstable-v4\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 725892165292113192, "profile": 15657897354478470176, "path": 4360627559389596061, "deps": [[5070769681332304831, "once_cell", false, 8348696261388979316], [5841926810058920975, "strsim", false, 16598019073523580460], [10058577953979766589, "atty", false, 14446544102526546006], [10435729446543529114, "bitflags", false, 14830140219543786391], [12902659978838094914, "termcolor", false, 6230942820611893881], [13685676330757124427, "textwrap", false, 7904797496112232539], [14923790796823607459, "indexmap", false, 8163083398758922520], [15944592714770878610, "clap_lex", false, 15503111058207813115]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-381267eb6689b0af/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}