{"rustc": 11410426090777951712, "features": "[\"default\", \"digest\", \"std\", \"traits-preview\"]", "declared_features": "[\"default\", \"digest\", \"mmap\", \"neon\", \"no_avx2\", \"no_avx512\", \"no_neon\", \"no_sse2\", \"no_sse41\", \"prefer_intrinsics\", \"pure\", \"rayon\", \"serde\", \"std\", \"traits-preview\", \"zeroize\"]", "target": 11963615372568355417, "profile": 2241668132362809309, "path": 4615194916557767051, "deps": [[1640307407508065381, "constant_time_eq", false, 10856647273647118448], [2891665871583465023, "build_script_build", false, 6655485820769239354], [9529943735784919782, "arrayref", false, 15227558456134739678], [10411997081178400487, "cfg_if", false, 18134666949081840738], [13847662864258534762, "arrayvec", false, 11168879829311016293], [17475753849556516473, "digest", false, 15902306368816772127]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/blake3-a68227870d754c7b/dep-lib-blake3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}