{"rustc": 11410426090777951712, "features": "[\"bytemuck_derive\", \"derive\"]", "declared_features": "[\"aarch64_simd\", \"align_offset\", \"alloc_uninit\", \"bytemuck_derive\", \"const_zeroed\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"latest_stable_rust\", \"min_const_generics\", \"must_cast\", \"nightly_docs\", \"nightly_float\", \"nightly_portable_simd\", \"nightly_stdsimd\", \"track_caller\", \"transparentwrapper_extra\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_atomics\", \"zeroable_maybe_uninit\"]", "target": 5195934831136530909, "profile": 639140734147086, "path": 129260061607804224, "deps": [[8702147382366870207, "bytemuck_derive", false, 1626256882799560345]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/bytemuck-ec4636d99aef4510/dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}