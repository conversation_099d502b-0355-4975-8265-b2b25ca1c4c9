{"rustc": 11410426090777951712, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 15657897354478470176, "path": 12714231586579680055, "deps": [[477150410136574819, "ark_ff_macros", false, 11079272496071403689], [2932480923465029663, "zeroize", false, 15371300713117404026], [5157631553186200874, "num_traits", false, 8022534605595409732], [11903278875415370753, "itertools", false, 2010533251943314243], [12528732512569713347, "num_bigint", false, 8980111594171540213], [13859769749131231458, "derivative", false, 3611229032565736103], [15179503056858879355, "ark_std", false, 13836943757611864263], [16925068697324277505, "ark_serialize", false, 10019833001616197297], [17475753849556516473, "digest", false, 14659021743208840920], [17605717126308396068, "paste", false, 13983389057743216358], [17996237327373919127, "ark_ff_asm", false, 2405538220987190340]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-ff-36e0125512125845/dep-lib-ark_ff", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}