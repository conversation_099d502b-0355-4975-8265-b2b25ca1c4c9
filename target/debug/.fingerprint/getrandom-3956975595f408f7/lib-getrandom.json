{"rustc": 11410426090777951712, "features": "[\"dummy\", \"std\"]", "declared_features": "[\"bindgen\", \"compiler_builtins\", \"core\", \"dummy\", \"js-sys\", \"log\", \"rustc-dep-of-std\", \"std\", \"stdweb\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 3140061874755240240, "profile": 15657897354478470176, "path": 10371856813955477107, "deps": [[2924422107542798392, "libc", false, 18050327750381138959], [5170503507811329045, "build_script_build", false, 12377097025811912770], [10411997081178400487, "cfg_if", false, 17983149432392734001]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/getrandom-3956975595f408f7/dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}