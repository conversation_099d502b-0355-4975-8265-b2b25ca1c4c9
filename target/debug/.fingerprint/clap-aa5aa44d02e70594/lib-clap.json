{"rustc": 11410426090777951712, "features": "[\"ansi_term\", \"atty\", \"color\", \"default\", \"strsim\", \"suggestions\", \"vec_map\"]", "declared_features": "[\"ansi_term\", \"atty\", \"clippy\", \"color\", \"debug\", \"default\", \"doc\", \"nightly\", \"no_cargo\", \"strsim\", \"suggestions\", \"term_size\", \"unstable\", \"vec_map\", \"wrap_help\", \"yaml\", \"yaml-rust\"]", "target": 12198692761336931930, "profile": 2241668132362809309, "path": 618277348759997503, "deps": [[1322514204948454048, "unicode_width", false, 1464418238285168599], [1810510990979880151, "ansi_term", false, 5883808024335260596], [6485010074357387197, "textwrap", false, 6958033696708727307], [10058577953979766589, "atty", false, 17935376274079002598], [10110425334065384495, "strsim", false, 7361900367155416021], [10435729446543529114, "bitflags", false, 18188691524635306757], [14451951854123638585, "vec_map", false, 18004871688773808028]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/clap-aa5aa44d02e70594/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}