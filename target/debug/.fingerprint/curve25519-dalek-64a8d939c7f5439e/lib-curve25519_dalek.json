{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"serde\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 2241668132362809309, "path": 8286721376779779491, "deps": [[1740877332521282793, "rand_core", false, 6215821917589840998], [2932480923465029663, "zeroize", false, 12065852831587227268], [3712811570531045576, "byteorder", false, 2527755583572649521], [6374421995994392543, "digest", false, 648177875051967704], [7719821159916746520, "subtle", false, 4241827732877797637], [10967960060725374459, "serde", false, 1794299219513741103]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-64a8d939c7f5439e/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}