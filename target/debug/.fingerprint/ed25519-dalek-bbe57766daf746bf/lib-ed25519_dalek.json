{"rustc": 11410426090777951712, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 16409354033026609460, "profile": 15657897354478470176, "path": 6138561264400601437, "deps": [[2932480923465029663, "zeroize", false, 15371300713117404026], [4731167174326621189, "rand", false, 17509847239581007800], [9431183304631869056, "curve25519_dalek", false, 13093291353235032497], [10967960060725374459, "serde_crate", false, 1837961595168625053], [11472355562936271783, "sha2", false, 4800024483440081938], [16629266738323756185, "ed25519", false, 11008670937106774262]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ed25519-dalek-bbe57766daf746bf/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}