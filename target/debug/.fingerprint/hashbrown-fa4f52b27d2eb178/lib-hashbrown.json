{"rustc": 11410426090777951712, "features": "[]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 15657897354478470176, "path": 7796880677095523143, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-fa4f52b27d2eb178/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}