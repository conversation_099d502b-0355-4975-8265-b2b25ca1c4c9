{"rustc": 11410426090777951712, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\", \"zlib\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lzma\", \"tokio\", \"xz\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 2241668132362809309, "path": 8636499643481877149, "deps": [[233996378138764347, "flate2", false, 17000234394522786607], [2877347214279964928, "pin_project_lite", false, 13830041932429296467], [3107892209185275978, "tokio", false, 16275644833075043944], [3129130049864710036, "memchr", false, 16890423447660437951], [7620660491849607393, "futures_core", false, 4368541333223789362], [14132538657330703225, "brotli", false, 4152224648713828579]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/async-compression-b713d583fa06709d/dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}