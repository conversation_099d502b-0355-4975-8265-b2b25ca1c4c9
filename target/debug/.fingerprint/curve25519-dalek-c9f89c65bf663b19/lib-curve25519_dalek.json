{"rustc": 11410426090777951712, "features": "[\"alloc\", \"default\", \"serde\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 4744499769514376500, "profile": 15657897354478470176, "path": 8286721376779779491, "deps": [[1740877332521282793, "rand_core", false, 14605077679506829308], [2932480923465029663, "zeroize", false, 15371300713117404026], [3712811570531045576, "byteorder", false, 12848468785084918029], [6374421995994392543, "digest", false, 11520441538129300845], [7719821159916746520, "subtle", false, 16203087294542188753], [10967960060725374459, "serde", false, 1837961595168625053]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/curve25519-dalek-c9f89c65bf663b19/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}