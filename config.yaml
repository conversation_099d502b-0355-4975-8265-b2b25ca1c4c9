# 套利机器人全局配置
poll_interval_ms: 5  # 默认轮询间隔(毫秒)
jito_tip_percentages:  # Jito小费百分比列表(随机选择)
  - 0.22
  - 0.34
  - 0.46
  - 0.66

# 自定义RPC URL(可选)
rpc_url: "http://*************:8899"
jupiter_api_url: "http://127.0.0.1:8080"
jito_rpc_url: "https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles"

# 多个Jito端点配置
jito_rpc_urls:
  - "https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles"
#  - "https://ny.mainnet.block-engine.jito.wtf/api/v1/bundles"
  - "https://amsterdam.mainnet.block-engine.jito.wtf/api/v1/bundles"
#  - "https://slc.mainnet.block-engine.jito.wtf/api/v1/bundles"
  # 可以添加更多Jito端点...

# 代理配置
use_proxy: true                    # 是否启用代理（true=使用代理，false=使用IP轮询）
proxy_file: "daili.txt"           # 代理文件路径（相对于项目根目录）

# 价格更新间隔(可选，默认30秒)
price_update_interval_ms: 33000

# 套利代币对列表
arb_pairs:
  # USDC -> 代币1 -> USDC (活跃度高，轮询间隔短)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump"
    amounts: [*********, *********, *********, ********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]
    only_direct_routes: true
    max_accounts: 20
    name: "USDC-Token1"
    poll_interval_ms: 2  # 每3毫秒检查一次(高频)
    enable_kamino: false   # 是否启用闪电贷（true=使用闪电贷，false=不使用）示例给ai看的
  
  # WSOL -> 代币3 -> WSOL (活跃度较低)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "So11111111111111111111111111111111111111112"
    amounts: [*********, *********, ********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]
    only_direct_routes: true
    max_accounts: 20
    name: "WSOL-Token3"
    poll_interval_ms: 1  # 每15毫秒检查一次(低频)
    enable_kamino: true   # 是否启用闪电贷（true=使用闪电贷，false=不使用）示例给ai看的

  # WSOL -> 代币3 -> WSOL (活跃度较低)
  - input_mint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
    output_mint: "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
    amounts: [*********, *********, ********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********, *********]
    only_direct_routes: true
    max_accounts: 20
    name: "USDC-Token2"
    poll_interval_ms: 15  # 每15毫秒检查一次(低频)

